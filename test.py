import requests

cookies = {
    'ambition_guru_session': 'eyJpdiI6Iit5bytVdzZLNHZIRWUzYWxyUjV4Vmc9PSIsInZhbHVlIjoiMXpqd1J4aHlaNXpGMlYwYVczMjFhcWxMNGlCTldjckNFRG5samJuYzdSeHhLa1k3bkZMa0JwSUx1bmNNUklCOFdYdFdtSjZYZkNTWEx2UW1aUlA5K3loQXZzUXdXZ0NmaW5Hb3ZOdzRUTGVoRTMyc2RSaUUvM1VKbTFlc1JwdzEiLCJtYWMiOiJmOWY1ZTZiZDQ2MGMwZGMzYjA4ZmUzMzQwMzUwNGY3MjRiOGE5MmYzMGJmNGIyN2JmOTA0ZmQ2M2UzOTA0MzFmIiwidGFnIjoiIn0%3D',
}

headers = {
    # 'Cookie': 'ambition_guru_session=eyJpdiI6Iit5bytVdzZLNHZIRWUzYWxyUjV4Vmc9PSIsInZhbHVlIjoiMXpqd1J4aHlaNXpGMlYwYVczMjFhcWxMNGlCTldjckNFRG5samJuYzdSeHhLa1k3bkZMa0JwSUx1bmNNUklCOFdYdFdtSjZYZkNTWEx2UW1aUlA5K3loQXZzUXdXZ0NmaW5Hb3ZOdzRUTGVoRTMyc2RSaUUvM1VKbTFlc1JwdzEiLCJtYWMiOiJmOWY1ZTZiZDQ2MGMwZGMzYjA4ZmUzMzQwMzUwNGY3MjRiOGE5MmYzMGJmNGIyN2JmOTA0ZmQ2M2UzOTA0MzFmIiwidGFnIjoiIn0%3D',
}

# Function to fetch all pages of data with error handling
def fetch_all_discussions(max_pages=50):
    all_data = []
    page = 1

    while page <= max_pages:
        url = f'https://api-adm.ambition.guru/api/next-ai/discussions?page={page}'
        print(f"Fetching page {page}...")

        try:
            response = requests.get(url, cookies=cookies, headers=headers, timeout=30)

            if response.status_code != 200:
                print(f"Error fetching page {page}: {response.status_code}")
                break

            full_response = response.json()
            page_data = full_response["data"]

            if not page_data:  # No more data
                print(f"No more data found at page {page}")
                break

            all_data.extend(page_data)
            print(f"Page {page}: {len(page_data)} items (Total so far: {len(all_data)})")

            # Check if there's a next page
            next_url = full_response.get("links", {}).get("next")
            if not next_url:
                print("No next page found")
                break

            page += 1

        except requests.exceptions.RequestException as e:
            print(f"Error fetching page {page}: {e}")
            print(f"Stopping at page {page-1}. Total items collected: {len(all_data)}")
            break

    return all_data

# Check if there are more pages by testing page 101
def check_more_pages():
    try:
        url = 'https://api-adm.ambition.guru/api/next-ai/discussions?page=101'
        response = requests.get(url, cookies=cookies, headers=headers, timeout=30)

        if response.status_code == 200:
            full_response = response.json()
            page_data = full_response["data"]

            if page_data:
                print(f"Page 101 has {len(page_data)} items - there is more data available!")
                return True
            else:
                print("Page 101 is empty - we have fetched all available data.")
                return False
        else:
            print(f"Page 101 returned status code: {response.status_code}")
            return False
    except Exception as e:
        print(f"Error checking page 101: {e}")
        return False

# Check if there's more data
print("Checking if there are more pages beyond 100...")
has_more = check_more_pages()

# Fetch all data from all pages (increase limit to get more data)
data = fetch_all_discussions(max_pages=500)  # Increased to 500 pages to get more data
print(f"\nTotal items fetched: {len(data)}")

if has_more:
    print("\n⚠️  WARNING: There appears to be more data available beyond page 100!")
    print("To fetch all data, you can increase the max_pages parameter or remove the limit entirely.")
else:
    print("\n✅ All available data has been fetched.")

import pandas as pd

# Extract data into the desired format
extracted_data = []

for item in data:
    # Get basic fields
    record = {
        'id': item.get('id'),
        'description': item.get('description'),
        'path': None  # Default to None
    }

    # Extract path from question_symbols if available
    question_symbols = item.get('question_symbols', [])
    if question_symbols and len(question_symbols) > 0:
        # Get the path from the first question symbol
        record['path'] = question_symbols[0].get('path')

    extracted_data.append(record)

# Create DataFrame
df = pd.DataFrame(extracted_data)

print("DataFrame created with columns:", df.columns.tolist())
print("\nDataFrame shape:", df.shape)
print("\nFirst few rows:")
print(df.head())

# Display the full DataFrame
print("\nComplete DataFrame:")
print(df)
df.to_csv('discussions_data.csv', index=False)