import requests

cookies = {
    'ambition_guru_session': 'eyJpdiI6Iit5bytVdzZLNHZIRWUzYWxyUjV4Vmc9PSIsInZhbHVlIjoiMXpqd1J4aHlaNXpGMlYwYVczMjFhcWxMNGlCTldjckNFRG5samJuYzdSeHhLa1k3bkZMa0JwSUx1bmNNUklCOFdYdFdtSjZYZkNTWEx2UW1aUlA5K3loQXZzUXdXZ0NmaW5Hb3ZOdzRUTGVoRTMyc2RSaUUvM1VKbTFlc1JwdzEiLCJtYWMiOiJmOWY1ZTZiZDQ2MGMwZGMzYjA4ZmUzMzQwMzUwNGY3MjRiOGE5MmYzMGJmNGIyN2JmOTA0ZmQ2M2UzOTA0MzFmIiwidGFnIjoiIn0%3D',
}

headers = {
    # 'Cookie': 'ambition_guru_session=eyJpdiI6Iit5bytVdzZLNHZIRWUzYWxyUjV4Vmc9PSIsInZhbHVlIjoiMXpqd1J4aHlaNXpGMlYwYVczMjFhcWxMNGlCTldjckNFRG5samJuYzdSeHhLa1k3bkZMa0JwSUx1bmNNUklCOFdYdFdtSjZYZkNTWEx2UW1aUlA5K3loQXZzUXdXZ0NmaW5Hb3ZOdzRUTGVoRTMyc2RSaUUvM1VKbTFlc1JwdzEiLCJtYWMiOiJmOWY1ZTZiZDQ2MGMwZGMzYjA4ZmUzMzQwMzUwNGY3MjRiOGE5MmYzMGJmNGIyN2JmOTA0ZmQ2M2UzOTA0MzFmIiwidGFnIjoiIn0%3D',
}

response = requests.get('https://api-adm.ambition.guru/api/next-ai/discussions', cookies=cookies, headers=headers)


data = response.json()["data"]

import pandas as pd

# Extract data into the desired format
extracted_data = []

for item in data:
    # Get basic fields
    record = {
        'id': item.get('id'),
        'description': item.get('description'),
        'path': None  # Default to None
    }

    # Extract path from question_symbols if available
    question_symbols = item.get('question_symbols', [])
    if question_symbols and len(question_symbols) > 0:
        # Get the path from the first question symbol
        record['path'] = question_symbols[0].get('path')

    extracted_data.append(record)

# Create DataFrame
df = pd.DataFrame(extracted_data)

print("DataFrame created with columns:", df.columns.tolist())
print("\nDataFrame shape:", df.shape)
print("\nFirst few rows:")
print(df.head())

# Display the full DataFrame
print("\nComplete DataFrame:")
print(df)
df.to_csv('discussions_data.csv', index=False)