import requests

cookies = {
    'ambition_guru_session': 'eyJpdiI6Iit5bytVdzZLNHZIRWUzYWxyUjV4Vmc9PSIsInZhbHVlIjoiMXpqd1J4aHlaNXpGMlYwYVczMjFhcWxMNGlCTldjckNFRG5samJuYzdSeHhLa1k3bkZMa0JwSUx1bmNNUklCOFdYdFdtSjZYZkNTWEx2UW1aUlA5K3loQXZzUXdXZ0NmaW5Hb3ZOdzRUTGVoRTMyc2RSaUUvM1VKbTFlc1JwdzEiLCJtYWMiOiJmOWY1ZTZiZDQ2MGMwZGMzYjA4ZmUzMzQwMzUwNGY3MjRiOGE5MmYzMGJmNGIyN2JmOTA0ZmQ2M2UzOTA0MzFmIiwidGFnIjoiIn0%3D',
}

headers = {
    # 'Cookie': 'ambition_guru_session=eyJpdiI6Iit5bytVdzZLNHZIRWUzYWxyUjV4Vmc9PSIsInZhbHVlIjoiMXpqd1J4aHlaNXpGMlYwYVczMjFhcWxMNGlCTldjckNFRG5samJuYzdSeHhLa1k3bkZMa0JwSUx1bmNNUklCOFdYdFdtSjZYZkNTWEx2UW1aUlA5K3loQXZzUXdXZ0NmaW5Hb3ZOdzRUTGVoRTMyc2RSaUUvM1VKbTFlc1JwdzEiLCJtYWMiOiJmOWY1ZTZiZDQ2MGMwZGMzYjA4ZmUzMzQwMzUwNGY3MjRiOGE5MmYzMGJmNGIyN2JmOTA0ZmQ2M2UzOTA0MzFmIiwidGFnIjoiIn0%3D',
}

response = requests.get('https://api-adm.ambition.guru/api/next-ai/discussions', cookies=cookies, headers=headers)


data = response.json()["data"]

import pandas as pd

# Normalize the JSON data and extract the fields you want
df = pd.json_normalize(data)

# Select only the columns you need: id, title, description, question_symbol, and image path