import requests

cookies = {
    'ambition_guru_session': 'eyJpdiI6Iit5bytVdzZLNHZIRWUzYWxyUjV4Vmc9PSIsInZhbHVlIjoiMXpqd1J4aHlaNXpGMlYwYVczMjFhcWxMNGlCTldjckNFRG5samJuYzdSeHhLa1k3bkZMa0JwSUx1bmNNUklCOFdYdFdtSjZYZkNTWEx2UW1aUlA5K3loQXZzUXdXZ0NmaW5Hb3ZOdzRUTGVoRTMyc2RSaUUvM1VKbTFlc1JwdzEiLCJtYWMiOiJmOWY1ZTZiZDQ2MGMwZGMzYjA4ZmUzMzQwMzUwNGY3MjRiOGE5MmYzMGJmNGIyN2JmOTA0ZmQ2M2UzOTA0MzFmIiwidGFnIjoiIn0%3D',
}

headers = {
    # 'Cookie': 'ambition_guru_session=eyJpdiI6Iit5bytVdzZLNHZIRWUzYWxyUjV4Vmc9PSIsInZhbHVlIjoiMXpqd1J4aHlaNXpGMlYwYVczMjFhcWxMNGlCTldjckNFRG5samJuYzdSeHhLa1k3bkZMa0JwSUx1bmNNUklCOFdYdFdtSjZYZkNTWEx2UW1aUlA5K3loQXZzUXdXZ0NmaW5Hb3ZOdzRUTGVoRTMyc2RSaUUvM1VKbTFlc1JwdzEiLCJtYWMiOiJmOWY1ZTZiZDQ2MGMwZGMzYjA4ZmUzMzQwMzUwNGY3MjRiOGE5MmYzMGJmNGIyN2JmOTA0ZmQ2M2UzOTA0MzFmIiwidGFnIjoiIn0%3D',
}

response = requests.get('https://api-adm.ambition.guru/api/next-ai/discussions', cookies=cookies, headers=headers)


data = response.json()["data"]

len(data)

import pandas as pd

# Normalize the JSON data and extract the fields you want
df = pd.json_normalize(data)

# Select only the columns you need: id, title, description, question_symbol, and image path
columns_to_keep = []
available_columns = df.columns.tolist()

# Check which columns exist and add them
if 'id' in available_columns:
    columns_to_keep.append('id')
if 'title' in available_columns:
    columns_to_keep.append('title')
if 'description' in available_columns:
    columns_to_keep.append('description')
if 'question_symbol' in available_columns:
    columns_to_keep.append('question_symbol')

# Look for image path fields and question_symbol path fields
image_columns = [col for col in available_columns if 'image' in col.lower() or 'path' in col.lower()]
question_symbol_columns = [col for col in available_columns if 'question_symbol' in col.lower() and 'path' in col.lower()]

columns_to_keep.extend(image_columns)
columns_to_keep.extend(question_symbol_columns)

# Remove duplicates while preserving order
columns_to_keep = list(dict.fromkeys(columns_to_keep))

# Create filtered dataframe
df_filtered = df[columns_to_keep] if columns_to_keep else df

# Save to CSV
df_filtered.to_csv('discussions_data.csv', index=False)

# Display the dataframe
print(f"Columns found: {available_columns}")
print(f"Selected columns: {columns_to_keep}")
df_filtered.head()

